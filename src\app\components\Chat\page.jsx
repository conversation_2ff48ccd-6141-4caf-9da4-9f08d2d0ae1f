"use client";
import React, { useState, useEffect, useRef } from "react";
import styles from "./chat.module.css";
import { StreamChat } from "stream-chat";
import "stream-chat-react/dist/css/index.css";
import axios from "axios"; // Import Axios
import {
  Chat,
  Channel,
  Window,
  TypingIndicator,
  MessageList,
  MessageInput,
  MessageInputFlat,
  withChannelContext,
} from "stream-chat-react";

let chatClient;

function App({ Recipientname, orderNumber }) {
  const [email, setEmail] = useState("");
  const [isInitialized, setIsInitialized] = useState(false);
  const chatContainerRef = useRef(null);
  var fName;
  var lName;
  var user_email;
  if (typeof window !== "undefined") {
    fName = localStorage.getItem("userName");
    lName = localStorage.getItem("lastname")?.toLowerCase();
    user_email = localStorage.getItem("userEmail")?.toLowerCase();
  }
  const [firstName, setFirstName] = useState(fName);
  const [lastName, setLastName] = useState(lName);
  const [channel, setChannel] = useState(null);

  const register = async (e) => {
    try {
      const BaseURL = process.env.NEXT_PUBLIC_Base_URL;
      const response = await axios.post(`${BaseURL}/chat-info/`, {
        order_number: orderNumber,
        email: user_email,
      });

      const { data, status } = response;

      if (status === 200) {
        const { user_id, channel_id, peername, chat_token } = data.data;

        chatClient = new StreamChat("yj2u7nnanuvb");

        await chatClient.connectUser(
          {
            id: user_id,
            name: firstName,
          },
          chat_token
        );
        const channel = chatClient.channel("messaging", channel_id, {
          name: `Chat with ${user_id}`,
        });

        await channel.watch();
        setChannel(channel);
        // Add a small delay to prevent auto-scroll on initial load
        setTimeout(() => {
          setIsInitialized(true);
          // Prevent any scroll behavior after initialization
          if (chatContainerRef.current) {
            const messageList = chatContainerRef.current.querySelector('.str-chat__message-list');
            if (messageList) {
              messageList.style.scrollBehavior = 'auto';
            }
          }
        }, 100);
      } else {
        console.error("Login unsuccessful: ", data.message);
      }
    } catch (error) {
      console.error("Error occurred during registration: ", error); // Log the error
    }
  };

  useEffect(() => {
    register();
  }, []);

  if (channel && isInitialized) {
    return (
      <Chat client={chatClient} theme="commerce light">
        <Channel channel={channel}>
          <div className={styles.chatCont} ref={chatContainerRef}>
            <Window>
              <div className={styles.stream_header}>
                <div className={styles.str_header_left}>
                  <p className={styles.stream_header_left_title}>
                    Peer to Peer Chat
                  </p>
                </div>
                <div className={styles.str_chat__header_livestream_right}>
                  Welcome, {chatClient?.user?.name}
                </div>
              </div>
              <MessageList
                typingIndicator={TypingIndicator}
                scrollToLatestMessageOnFocus={false}
              />
              <MessageInput Input={MessageInputFlat} focus />
            </Window>
          </div>
        </Channel>
      </Chat>
    );
  }

  // Show loading state while initializing
  if (channel && !isInitialized) {
    return (
      <div className={styles.chatCont}>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          color: '#666'
        }}>
          Loading chat...
        </div>
      </div>
    );
  }
}

export default App;
